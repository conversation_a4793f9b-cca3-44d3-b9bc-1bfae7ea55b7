@use '../../../../styles/shared.scss' as *;

/* Add styles for the popup */
.popup {
  position: fixed; 
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 80%; 
  max-width: 400px; 
  background-color: white;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5); 
  z-index: 100; 
}

.popup-content {
  text-align: center;
}

.close {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 20px;
  cursor: pointer;
}

.button-group {
  display: inline-flex !important;
  flex-direction: row-reverse !important;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: nowrap !important;
  align-items: center;
  white-space: nowrap;
}

.row-view .button-group,
.row-view-table .button-group,
.row-view-nested .button-group,
.nested-group-section .button-group,
.grouped-field-section .button-group {
  display: inline-flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  margin: 0;
  padding: 0;
}

@media (max-width: 768px) {
  .button-group {
    gap: 6px;
  }
}

@media (max-width: 480px) {
  .button-group {
    gap: 4px;
  }
}

.success-message {
  color: #28a745;
  font-size: 14px;
  font-weight: bold;
  padding: 10px;
  border: 1px solid #c3e6cb;
  background-color: #d4edda;
  border-radius: 4px;
  margin-bottom: 10px;
}

.error-message {
  background-color: #fdd; /* Light red background */
  border: 1px solid #faa; /* Red border */
  color: #a00; /* Dark red text color */
  padding: 10px;
  margin-bottom: 10px; 
  border-radius: 4px; /* Slightly rounded corners */
}

/* Material snackbar styling */
.mat-mdc-snack-bar-container {
  &.success-snackbar {
    --mdc-snackbar-container-color: #4caf50;
    --mat-mdc-snack-bar-button-color: #fff;
    --mdc-snackbar-supporting-text-color: #fff;
  }

  &.error-snackbar {
    --mdc-snackbar-container-color: #f44336;
    --mat-mdc-snack-bar-button-color: #fff;
    --mdc-snackbar-supporting-text-color: #fff;
  }
}

/* Base form action button styles */
.form-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 2px solid;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
  min-width: 36px;
  min-height: 36px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;

  mat-icon {
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1 !important;
    margin: 0;
    padding: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

/* High specificity selectors to override global styles */
.form-action-button .mat-icon,
.form-action-button mat-icon.material-icons,
.form-action-button .mat-icon.material-icons {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
  line-height: 1 !important;
  min-width: 16px !important;
  min-height: 16px !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Toggle View Button - Teal theme */
.form-action-button.toggle-view-button {
  border-color: #009688;
  background: linear-gradient(135deg, #009688 0%, #00796B 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #00796B 0%, #004D40 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 150, 136, 0.3);
  }
}

/* Submit Button - Green theme */
.form-action-button.submit-button {
  border-color: #4CAF50;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
  }
  
  &:disabled {
    background: #cccccc !important;
    color: #666666 !important;
    border-color: #cccccc !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }
}

/* Validate Button - Blue theme */
.form-action-button.validate-button {
  border-color: #2196F3;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #1976D2 0%, #1565C0 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(33, 150, 243, 0.3);
  }
}

/* Authorize Button - Purple theme */
.form-action-button.authorize-button {
  border-color: #9C27B0;
  background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #7B1FA2 0%, #6A1B9A 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(156, 39, 176, 0.3);
  }
}

/* Back Button - Gray theme */
.form-action-button.back-button {
  border-color: #607D8B;
  background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #455A64 0%, #37474F 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(96, 125, 139, 0.3);
  }
}

/* Reject Button - Red theme */
.form-action-button.reject-button {
  border-color: #F44336;
  background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #D32F2F 0%, #C62828 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
  }
}

/* Delete Button - Dark Red theme */
.form-action-button.delete-button {
  border-color: #D32F2F;
  background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #B71C1C 0%, #8E0000 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(211, 47, 47, 0.3);
  }
}

/* Responsive sizing for form action buttons */
@media (max-width: 1024px) {
  .form-action-button {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;
  }

  .form-action-button mat-icon,
  .form-action-button .mat-icon,
  .form-action-button mat-icon.material-icons,
  .form-action-button .mat-icon.material-icons {
    font-size: 14px !important;
    width: 14px !important;
    height: 14px !important;
    min-width: 14px !important;
    min-height: 14px !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

@media (max-width: 768px) {
  .form-action-button {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;

    mat-icon,
    .mat-icon,
    mat-icon.material-icons,
    .mat-icon.material-icons {
      font-size: 14px !important;
      width: 14px !important;
      height: 14px !important;
      min-width: 14px !important;
      min-height: 14px !important;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

@media (max-width: 480px) {
  .form-action-button {
    width: 28px;
    height: 28px;
    min-width: 28px;
    min-height: 28px;

    mat-icon,
    .mat-icon,
    mat-icon.material-icons,
    .mat-icon.material-icons {
      font-size: 12px !important;
      width: 12px !important;
      height: 12px !important;
      min-width: 12px !important;
      min-height: 12px !important;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

@media (max-width: 360px) {
  .form-action-button {
    width: 24px;
    height: 24px;
    min-width: 24px;
    min-height: 24px;
  }

  .form-action-button mat-icon,
  .form-action-button .mat-icon,
  .form-action-button mat-icon.material-icons,
  .form-action-button .mat-icon.material-icons {
    font-size: 10px !important;
    width: 10px !important;
    height: 10px !important;
    min-width: 10px !important;
    min-height: 10px !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
